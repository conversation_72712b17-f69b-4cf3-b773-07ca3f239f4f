apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: ai-spring-backend-base
  annotations:
    config.kubernetes.io/local-config: "true"

# Base resources for ai-spring-backend
resources:
- namespace.yaml
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml

# Common labels applied to all resources
commonLabels:
  app.kubernetes.io/managed-by: argocd
  app.kubernetes.io/part-of: ai-spring-backend

# Common annotations applied to all resources
commonAnnotations:
  argocd.argoproj.io/tracking-id: "ai-spring-backend:ai-spring-backend-dev"

# Namespace for all resources
namespace: ai-spring-backend-dev

# Images that can be customized by overlays
images:
- name: CONTAINER_IMAGE
  newTag: latest

# ConfigMap and Secret generators can be added by overlays
configMapGenerator: []
secretGenerator: []

# Patches that can be applied by overlays
patches: []
